#ifndef SIMPLE_TRANSLITERATOR_H
#define SIMPLE_TRANSLITERATOR_H

#include <string>
#include <vector>
#include <unordered_map>
#include <algorithm>

/**
 * Simplified Tamil Transliterator for testing and fallback
 * This ensures the backend works while we debug the full SMC engine
 */
class SimpleTransliterator {
private:
    std::unordered_map<std::string, std::string> basicMappings;
    std::unordered_map<std::string, std::string> wordDictionary;
    
    void initializeMappings() {
        // Basic character mappings
        basicMappings["a"] = "அ";
        basicMappings["aa"] = "ஆ";
        basicMappings["i"] = "இ";
        basicMappings["ii"] = "ஈ";
        basicMappings["u"] = "உ";
        basicMappings["uu"] = "ஊ";
        basicMappings["e"] = "எ";
        basicMappings["ee"] = "ஏ";
        basicMappings["ai"] = "ஐ";
        basicMappings["o"] = "ஒ";
        basicMappings["oo"] = "ஓ";
        basicMappings["au"] = "ஔ";
        
        // Consonants
        basicMappings["k"] = "க்";
        basicMappings["g"] = "க்";
        basicMappings["ng"] = "ங்";
        basicMappings["ch"] = "ச்";
        basicMappings["c"] = "ச்";
        basicMappings["j"] = "ஜ்";
        basicMappings["nj"] = "ஞ்";
        basicMappings["t"] = "ட்";
        basicMappings["d"] = "ட்";
        basicMappings["n"] = "ன்";
        basicMappings["th"] = "த்";
        basicMappings["dh"] = "த்";
        basicMappings["nn"] = "ந்";
        basicMappings["p"] = "ப்";
        basicMappings["b"] = "ப்";
        basicMappings["m"] = "ம்";
        basicMappings["y"] = "ய்";
        basicMappings["r"] = "ர்";
        basicMappings["l"] = "ல்";
        basicMappings["v"] = "வ்";
        basicMappings["w"] = "வ்";
        basicMappings["sh"] = "ஷ்";
        basicMappings["s"] = "ச்";
        basicMappings["h"] = "ஹ்";
        basicMappings["ll"] = "ள்";
        basicMappings["rr"] = "ற்";
        basicMappings["nn"] = "ண்";
        basicMappings["zh"] = "ழ்";
        basicMappings["z"] = "ழ்";
        
        // Common words
        wordDictionary["vanakkam"] = "வணக்கம்";
        wordDictionary["nandri"] = "நன்றி";
        wordDictionary["tamil"] = "தமிழ்";
        wordDictionary["amma"] = "அம்மா";
        wordDictionary["appa"] = "அப்பா";
        wordDictionary["naan"] = "நான்";
        wordDictionary["neenga"] = "நீங்கள்";
        wordDictionary["enna"] = "என்ன";
        wordDictionary["eppo"] = "எப்போ";
        wordDictionary["enga"] = "எங்க";
        wordDictionary["ethu"] = "எது";
        wordDictionary["eppadi"] = "எப்படி";
        wordDictionary["yen"] = "ஏன்";
        wordDictionary["yaar"] = "யார்";
        wordDictionary["sollu"] = "சொல்லு";
        wordDictionary["paar"] = "பார்";
        wordDictionary["vaa"] = "வா";
        wordDictionary["po"] = "போ";
        wordDictionary["iru"] = "இரு";
        wordDictionary["vidu"] = "விடு";
        wordDictionary["kudu"] = "குடு";
        wordDictionary["edu"] = "எடு";
        wordDictionary["computer"] = "கம்ப்யூட்டர்";
        wordDictionary["mobile"] = "மொபைல்";
        wordDictionary["internet"] = "இண்டர்நெட்";
        wordDictionary["school"] = "ஸ்கூல்";
        wordDictionary["college"] = "கல்லூரி";
        wordDictionary["hospital"] = "மருத்துவமனை";
        wordDictionary["office"] = "அலுவலகம்";
        wordDictionary["house"] = "வீடு";
        wordDictionary["car"] = "கார்";
        wordDictionary["bus"] = "பஸ்";
        wordDictionary["train"] = "ரயில்";
        wordDictionary["book"] = "புத்தகம்";
        wordDictionary["pen"] = "பேனா";
        wordDictionary["water"] = "தண்ணீர்";
        wordDictionary["food"] = "உணவு";
        wordDictionary["rice"] = "சாதம்";
        wordDictionary["money"] = "பணம்";
        wordDictionary["time"] = "நேரம்";
        wordDictionary["work"] = "வேலை";
        wordDictionary["friend"] = "நண்பன்";
        wordDictionary["family"] = "குடும்பம்";
        wordDictionary["love"] = "காதல்";
        wordDictionary["happy"] = "சந்தோஷம்";
        wordDictionary["good"] = "நல்ல";
        wordDictionary["bad"] = "கெட்ட";
        wordDictionary["big"] = "பெரிய";
        wordDictionary["small"] = "சின்ன";
        wordDictionary["hello"] = "வணக்கம்";
        wordDictionary["thanks"] = "நன்றி";
        wordDictionary["welcome"] = "வரவேற்கிறோம்";
        wordDictionary["yes"] = "ஆம்";
        wordDictionary["no"] = "இல்லை";
    }
    
public:
    SimpleTransliterator() {
        initializeMappings();
    }
    
    std::string transliterate(const std::string& input) {
        if (input.empty()) return "";
        
        // Convert to lowercase for lookup
        std::string lowerInput = input;
        std::transform(lowerInput.begin(), lowerInput.end(), lowerInput.begin(), ::tolower);
        
        // Check word dictionary first
        auto wordIt = wordDictionary.find(lowerInput);
        if (wordIt != wordDictionary.end()) {
            return wordIt->second;
        }
        
        // Character-by-character transliteration
        std::string result;
        for (size_t i = 0; i < lowerInput.length(); ++i) {
            bool found = false;
            
            // Try longer patterns first
            for (int len = std::min(3, (int)(lowerInput.length() - i)); len >= 1; --len) {
                std::string substr = lowerInput.substr(i, len);
                auto it = basicMappings.find(substr);
                if (it != basicMappings.end()) {
                    result += it->second;
                    i += len - 1; // Skip processed characters
                    found = true;
                    break;
                }
            }
            
            if (!found) {
                // Keep original character if no mapping found
                result += lowerInput[i];
            }
        }
        
        return result;
    }
    
    std::vector<std::string> getSuggestions(const std::string& input, int maxSuggestions = 5) {
        std::vector<std::string> suggestions;
        
        // Add primary translation
        std::string primary = transliterate(input);
        suggestions.push_back(primary);
        
        // Add some variations
        std::string lowerInput = input;
        std::transform(lowerInput.begin(), lowerInput.end(), lowerInput.begin(), ::tolower);
        
        // Look for similar words in dictionary
        for (const auto& pair : wordDictionary) {
            if (pair.first.find(lowerInput) != std::string::npos || 
                lowerInput.find(pair.first) != std::string::npos) {
                if (std::find(suggestions.begin(), suggestions.end(), pair.second) == suggestions.end()) {
                    suggestions.push_back(pair.second);
                    if (suggestions.size() >= maxSuggestions) break;
                }
            }
        }
        
        return suggestions;
    }
    
    std::string transliterateWithContext(const std::string& input, const std::string& context = "") {
        // For now, just use basic transliteration
        return transliterate(input);
    }
    
    bool isInDictionary(const std::string& word) {
        std::string lowerWord = word;
        std::transform(lowerWord.begin(), lowerWord.end(), lowerWord.begin(), ::tolower);
        return wordDictionary.find(lowerWord) != wordDictionary.end();
    }
};

#endif // SIMPLE_TRANSLITERATOR_H
