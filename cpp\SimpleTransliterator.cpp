#include "SimpleTransliterator.h"
#include <algorithm>
#include <cctype>

SimpleTransliterator::SimpleTransliterator() {
    initializeBasicMappings();
}

void SimpleTransliterator::initializeBasicMappings() {
    // Basic character mappings for fallback
    basicMappings = {
        // Common words
        {"vanakkam", "வணக்கம்"}, {"nandri", "நன்றி"}, {"tamil", "தமிழ்"},
        {"amma", "அம்மா"}, {"appa", "அப்பா"}, {"hello", "வணக்கம்"},
        {"thanks", "நன்றி"}, {"yes", "ஆம்"}, {"no", "இல்லை"},
        
        // Basic vowels
        {"a", "அ"}, {"aa", "ஆ"}, {"i", "இ"}, {"ii", "ஈ"},
        {"u", "உ"}, {"uu", "ஊ"}, {"e", "எ"}, {"ee", "ஏ"},
        {"ai", "ஐ"}, {"o", "ஒ"}, {"oo", "ஓ"}, {"au", "ஔ"},
        
        // Basic consonants
        {"k", "க்"}, {"g", "க்"}, {"ch", "ச்"}, {"j", "ஜ்"},
        {"t", "ட்"}, {"d", "ட்"}, {"n", "ன்"}, {"th", "த்"},
        {"p", "ப்"}, {"b", "ப்"}, {"m", "ம்"}, {"y", "ய்"},
        {"r", "ர்"}, {"l", "ல்"}, {"v", "வ்"}, {"s", "ச்"},
        {"h", "ஹ்"}, {"z", "ழ்"}
    };
}

std::string SimpleTransliterator::transliterate(const std::string& input) {
    if (input.empty()) return "";
    
    std::string lowerInput = input;
    std::transform(lowerInput.begin(), lowerInput.end(), lowerInput.begin(), ::tolower);
    
    // Check for exact match first
    auto it = basicMappings.find(lowerInput);
    if (it != basicMappings.end()) {
        return it->second;
    }
    
    // Simple character-by-character mapping
    std::string result;
    for (size_t i = 0; i < lowerInput.length(); ) {
        bool found = false;
        
        // Try 2-character combinations first
        if (i + 1 < lowerInput.length()) {
            std::string twoChar = lowerInput.substr(i, 2);
            auto it2 = basicMappings.find(twoChar);
            if (it2 != basicMappings.end()) {
                result += it2->second;
                i += 2;
                found = true;
            }
        }
        
        // Try single character
        if (!found) {
            std::string oneChar = lowerInput.substr(i, 1);
            auto it1 = basicMappings.find(oneChar);
            if (it1 != basicMappings.end()) {
                result += it1->second;
            } else {
                result += oneChar;
            }
            i++;
        }
    }
    
    return result.empty() ? input : result;
}

std::vector<std::string> SimpleTransliterator::getSuggestions(const std::string& input, int maxSuggestions) {
    std::vector<std::string> suggestions;
    
    // Add primary translation
    std::string primary = transliterate(input);
    suggestions.push_back(primary);
    
    // Add some basic alternatives
    std::string lowerInput = input;
    std::transform(lowerInput.begin(), lowerInput.end(), lowerInput.begin(), ::tolower);
    
    if (lowerInput == "hello" || lowerInput == "hi") {
        suggestions.push_back("வணக்கம்");
    } else if (lowerInput == "thanks" || lowerInput == "thank you") {
        suggestions.push_back("நன்றி");
    }
    
    // Resize to max suggestions
    if (suggestions.size() > maxSuggestions) {
        suggestions.resize(maxSuggestions);
    }
    
    return suggestions;
}

bool SimpleTransliterator::isInDictionary(const std::string& word) {
    std::string lowerWord = word;
    std::transform(lowerWord.begin(), lowerWord.end(), lowerWord.begin(), ::tolower);
    return basicMappings.find(lowerWord) != basicMappings.end();
}
